"use client";

import { ArrowRightIcon } from "lucide-react";
import { motion } from "motion/react";

import MaskedDiv from "@/components/custom/ui/masked-div";

import AlgoliaBlueButton from "../custom/ui/algolia-blue-button";

export default function HeroSection() {
  return (
    <section className="relative px-4 py-32 sm:px-6 lg:px-8 lg:py-40">
      <div className="mx-auto max-w-6xl">
        {/* Content - Centered Layout like Linear */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.1 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className="text-center"
        >
          {/* Main Heading - Linear Style */}
          <h1 className="from-primary via-primary/80 to-primary/70 mb-6 bg-gradient-to-r bg-clip-text text-3xl leading-tight font-medium tracking-tight text-transparent sm:text-4xl lg:text-6xl">
            Next Core is a purpose-built tool for
            <br className="hidden sm:block" />
            <span className="sm:hidden"> </span>planning and building products
          </h1>

          {/* Description - Linear Style */}
          <p className="text-muted-foreground mx-auto mb-8 max-w-2xl text-base leading-relaxed sm:text-lg">
            Meet the complete Next.js boilerplate for modern SaaS development.
            <br className="hidden sm:block" />
            <span className="sm:hidden"> </span>Streamline authentication,
            payments, and product launches.
          </p>

          {/* CTA Buttons - Linear Style */}
          <div className="mb-16 flex flex-col gap-3 sm:flex-row sm:justify-center sm:gap-4">
            <AlgoliaBlueButton>Get Started</AlgoliaBlueButton>
            <button className="text-muted-foreground hover:text-foreground flex cursor-pointer items-center justify-center gap-2 text-sm font-medium transition-colors">
              Introducing Next Core for Agents
              <ArrowRightIcon className="h-4 w-4" />
            </button>
          </div>
        </motion.div>

        {/* Dashboard Mockup - Linear Style */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.1 }}
          transition={{ duration: 0.6, ease: "easeOut", delay: 0.2 }}
          className="relative"
        >
          <div className="relative mx-auto max-w-6xl">
            {/* Linear-style Dashboard Interface */}
            <MaskedDiv
              maskType="type-2"
              className="my-4"
            >
              <video
                className="cursor-pointer transition-all duration-300 hover:scale-105"
                autoPlay
                loop
                muted
              >
                <source
                  src="https://videos.pexels.com/video-files/18069232/18069232-uhd_2560_1440_24fps.mp4"
                  type="video/mp4"
                />
              </video>
            </MaskedDiv>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
